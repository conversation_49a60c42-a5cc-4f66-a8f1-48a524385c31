<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include 'db.php'; // doit définir $pdo

header('Content-Type: application/json');

try {
    $stmt = $pdo->query("SELECT id, nom FROM wilayas");
    $wilayas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode($wilayas);
} catch (PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}