<?php
include 'db.php';

header('Content-Type: application/json');

try {
    // Récupérer toutes les wilayas de la base de données
    $stmt = $pdo->query("SELECT id, nom FROM wilayas ORDER BY id");
    $wilayas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Coordonnées approximatives des frontières des wilayas algériennes
    $wilayaCoordinates = [
        'Béchar' => [
            [-2.5, 31.0], [-1.5, 31.0], [-1.0, 31.5], [-0.5, 32.0], 
            [-0.5, 32.5], [-1.0, 33.0], [-2.0, 33.0], [-3.0, 32.5], 
            [-3.5, 32.0], [-3.0, 31.5], [-2.5, 31.0]
        ],
        '<PERSON>rar' => [
            [-1.0, 27.0], [0.5, 27.0], [1.0, 27.5], [1.5, 28.0], 
            [1.5, 28.5], [1.0, 29.0], [0.0, 29.0], [-1.0, 28.5], 
            [-1.5, 28.0], [-1.0, 27.5], [-1.0, 27.0]
        ],
        '<PERSON>ger' => [
            [2.8, 36.6], [3.2, 36.6], [3.4, 36.8], [3.2, 37.0], 
            [2.8, 37.0], [2.6, 36.8], [2.8, 36.6]
        ],
        'Oran' => [
            [-1.0, 35.5], [-0.5, 35.5], [0.0, 35.8], [-0.2, 36.0], 
            [-0.8, 36.0], [-1.2, 35.8], [-1.0, 35.5]
        ],
        'Constantine' => [
            [6.4, 36.2], [6.8, 36.2], [7.0, 36.5], [6.8, 36.8], 
            [6.4, 36.8], [6.2, 36.5], [6.4, 36.2]
        ],
        'Annaba' => [
            [7.6, 36.8], [8.0, 36.8], [8.2, 37.0], [8.0, 37.2], 
            [7.6, 37.2], [7.4, 37.0], [7.6, 36.8]
        ],
        'Sétif' => [
            [5.2, 36.0], [5.8, 36.0], [6.0, 36.3], [5.8, 36.6], 
            [5.2, 36.6], [5.0, 36.3], [5.2, 36.0]
        ],
        'Batna' => [
            [5.8, 35.2], [6.4, 35.2], [6.6, 35.5], [6.4, 35.8], 
            [5.8, 35.8], [5.6, 35.5], [5.8, 35.2]
        ],
        'Biskra' => [
            [5.5, 34.5], [6.0, 34.5], [6.2, 34.8], [6.0, 35.1], 
            [5.5, 35.1], [5.3, 34.8], [5.5, 34.5]
        ],
        'Ouargla' => [
            [4.8, 31.5], [5.5, 31.5], [5.8, 32.0], [5.5, 32.5], 
            [4.8, 32.5], [4.5, 32.0], [4.8, 31.5]
        ]
    ];
    
    // Créer le GeoJSON
    $features = [];
    
    foreach ($wilayas as $wilaya) {
        $coordinates = $wilayaCoordinates[$wilaya['nom']] ?? [
            // Coordonnées par défaut si la wilaya n'est pas trouvée
            [[0, 0], [1, 0], [1, 1], [0, 1], [0, 0]]
        ];
        
        $features[] = [
            'type' => 'Feature',
            'properties' => [
                'id' => $wilaya['id'],
                'nom' => $wilaya['nom'],
                'code' => str_pad($wilaya['id'], 2, '0', STR_PAD_LEFT)
            ],
            'geometry' => [
                'type' => 'Polygon',
                'coordinates' => [$coordinates]
            ]
        ];
    }
    
    $geojson = [
        'type' => 'FeatureCollection',
        'features' => $features
    ];
    
    echo json_encode($geojson);
    
} catch (PDOException $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
