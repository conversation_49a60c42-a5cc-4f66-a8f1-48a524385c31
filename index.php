<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Carte Leaflet avec recherche</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <style>
    #map {
        height: 500px;
        width: 100%;
        margin-bottom: 10px;
    }

    #formContainer {
        display: none;
        position: fixed;
        top: 20%;
        left: 35%;
        background: white;
        border: 2px solid #444;
        padding: 15px;
        z-index: 9999;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    }

    #copyright {
        position: fixed;
        bottom: 10px;
        right: 10px;
        font-size: 13px;
        background: rgba(255, 255, 255, 0.8);
        padding: 6px 10px;
        border-radius: 5px;
        color: #333;
        font-weight: bold;
        z-index: 99999;
        pointer-events: none;
        user-select: none;
    }

    .export-buttons {
        margin-top: 10px;
        display: flex;
        gap: 10px;
    }

    /* Styles pour les marqueurs personnalisés */
    .custom-marker {
        background: transparent !important;
        border: none !important;
    }

    .custom-marker div {
        transition: transform 0.2s ease;
    }

    .custom-marker:hover div {
        transform: scale(1.1);
    }

    /* Styles pour les tooltips des wilayas */
    .wilaya-tooltip {
        background: rgba(46, 139, 87, 0.9) !important;
        border: none !important;
        border-radius: 4px !important;
        color: white !important;
        font-weight: bold !important;
        font-size: 12px !important;
        padding: 5px 8px !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3) !important;
    }

    .wilaya-tooltip::before {
        border-top-color: rgba(46, 139, 87, 0.9) !important;
    }

    .export-button {
        padding: 6px 12px;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .export-button:hover {
        background-color: #45a049;
    }

    .export-button-csv {
        background-color: #2196F3;
    }

    .export-button-csv:hover {
        background-color: #0b7dda;
    }

    .export-button-pdf {
        background-color: #f44336;
    }

    .export-button-pdf:hover {
        background-color: #d32f2f;
    }
    </style>

</head>

<body>

    <div id="searchBar">
        🔍 Rechercher :
        <input type="text" id="searchInput" placeholder="Nom..." />
        <label>Wilaya :</label>
        <select id="wilayaFilter" onchange="onWilayaChange()  ">
            <option value="">-- Toutes --</option>
        </select>
        <label>Commune : </label>
        <select id="communeFilter" onchange="filterMarkers()">
            <option value="">-- Toutes --</option>

        </select>

        Type :
        <select id="categoryFilter">
            <option value="">-- Toutes --</option>
            <option value="Alimentation Générale">Alimentation Générale</option>
            <option value="Boucherie ">Boucherie </option>
            <option value="Boulangerie">Boulangerie</option>
            <option value="Fruits et légumes">Fruits et légumes</option>
            <option value="Laiterie">Laiterie</option>
            <option value="Minoterie" selected="">Minoterie</option>
            <option value="Marché">Marché</option>
            <option value="Superette">Superette</option>
            <option value="Pharmacie">Pharmacie</option>
            <option value="Unités d'eau minérale">Unités d'eau minérale</option>
            <option value="Recharge téléphonique">Recharge téléphonique</option>
        </select>

        <div class="export-buttons">
            <button onclick="exportCSV()" class="export-button export-button-csv">📄 Exporter CSV</button>
            <button onclick="exportPDF()" class="export-button export-button-pdf">📑 Exporter PDF</button>
            <button onclick="toggleLegend()" class="export-button" style="background: #28a745;">🗺️ Légende</button>
            <button onclick="toggleWilayaBoundaries()" class="export-button" style="background: #17a2b8;">🗾 Masquer Frontières</button>
            <button onclick="loadTestBoundaries()" class="export-button" style="background: #ffc107;">🧪 Test Frontières</button>
            <button onclick="loadCountryBoundary()" class="export-button" style="background: #6f42c1;">🇩🇿 Algérie</button>
        </div>
    </div>


    <div id="formContainer">
        <h4>Ajouter une position</h4>
        <form id="positionForm">
            <label>Nom :</label><br>
            <input type="text" id="nom" required><br>
            <label>Type :</label><br>
            <select id="categorie">
                <option value="Alimentation Générale">Alimentation Générale</option>
                <option value="Boucherie ">Boucherie </option>
                <option value="Boulangerie">Boulangerie</option>
                <option value="Fruits et légumes">Fruits et légumes</option>
                <option value="Laiterie">Laiterie</option>
                <option value="Minoterie" selected="">Minoterie</option>
                <option value="Marché">Marché</option>
                <option value="Superette">Superette</option>
                <option value="Pharmacie">Pharmacie</option>
                <option value="Unités d'eau minérale">Unités d'eau minérale</option>

                <option value="Recharge téléphonique">Recharge téléphonique</option>
            </select><br><br>
            <label>Wilaya :</label>
            <select id="wilaya" onchange="loadCommunes()" required>

            </select><br><br>

            <label>Commune :</label>
            <select id="commune" required>
                <option value="">-- Choisir une commune --</option>
            </select><br><br>

            <label>Latitude :</label><br>
            <input type="text" id="lat"><br>
            <label>Longitude :</label><br>
            <input type="text" id="lng"><br><br>

            <button type="button" onclick="savePosition()">Enregistrer</button>

            <button type="button" onclick="cancelForm()">Annuler</button>
        </form>
    </div>


    <div id="map"></div>

    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <!-- Légende des icônes -->
    <div id="legend" style="
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid #ccc;
        border-radius: 8px;
        padding: 15px;
        max-width: 250px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
        font-size: 12px;
        max-height: 400px;
        overflow-y: auto;
    ">
        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">Légende des catégories</h4>
        <div id="legendContent"></div>
        <button onclick="toggleLegend()" style="
            margin-top: 10px;
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        ">Masquer</button>
    </div>

    <div id="copyright">
        © 2025 Boumares Abdelhafid. Tous droits réservés.
    </div>

    <script>
    let map = L.map('map').setView([32.5, 3.0], 6);
    let currentLatLng;
    let markers = [];

    // Configuration des icônes colorées pour chaque catégorie
    const categoryIcons = {
        'Alimentation Générale': {
            color: '#2E8B57', // Vert foncé
            icon: '🛒'
        },
        'Boucherie ': {
            color: '#DC143C', // Rouge
            icon: '🥩'
        },
        'Boulangerie': {
            color: '#DAA520', // Or
            icon: '🍞'
        },
        'Fruits et légumes': {
            color: '#32CD32', // Vert lime
            icon: '🥬'
        },
        'Laiterie': {
            color: '#87CEEB', // Bleu ciel
            icon: '🥛'
        },
        'Minoterie': {
            color: '#D2691E', // Chocolat
            icon: '🌾'
        },
        'Marché': {
            color: '#FF6347', // Tomate
            icon: '🏪'
        },
        'Superette': {
            color: '#4169E1', // Bleu royal
            icon: '🏬'
        },
        'Pharmacie': {
            color: '#00CED1', // Turquoise foncé
            icon: '💊'
        },
        'Unités d\'eau minérale': {
            color: '#1E90FF', // Bleu dodger
            icon: '💧'
        },
        'Recharge téléphonique': {
            color: '#9932CC', // Violet foncé
            icon: '📱'
        }
    };

    // Fonction pour créer une icône personnalisée
    function createCustomIcon(category) {
        const config = categoryIcons[category] || {
            color: '#666666',
            icon: '📍'
        };

        return L.divIcon({
            className: 'custom-marker',
            html: `
                <div style="
                    background-color: ${config.color};
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    border: 3px solid white;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    color: white;
                    font-weight: bold;
                ">
                    ${config.icon}
                </div>
            `,
            iconSize: [30, 30],
            iconAnchor: [15, 15],
            popupAnchor: [0, -15]
        });
    }

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap'
    }).addTo(map);

    let markerMap = {};
    let wilayaBoundaries = {}; // Pour stocker les frontières des wilayas
    let boundaryLayers = []; // Pour gérer l'affichage des frontières
    // Chargement des positions depuis la BDD
    function loadMarkers(wilayaId = 'all') {


        markers.forEach(m => map.removeLayer(m));
        markers = [];

        fetch(`get_positions.php?wilaya_id=${wilayaId}`)
            .then(res => res.json())
            .then(data => {
                data.forEach(pos => {
                    let marker = L.marker([pos.latitude, pos.longitude], {
                            id: pos.id,
                            icon: createCustomIcon(pos.categorie)
                        })

                        .addTo(map)
                        .bindTooltip(pos.nom, {
                            permanent: false,
                            direction: 'top'
                        })


                        .bindPopup(`
                            <div style="min-width: 200px;">
                                <b style="color: ${categoryIcons[pos.categorie]?.color || '#666'};">${pos.nom}</b><br>
                                <span style="display: inline-flex; align-items: center; margin: 5px 0;">
                                    <span style="margin-right: 5px;">${categoryIcons[pos.categorie]?.icon || '📍'}</span>
                                    <strong>Catégorie :</strong> ${pos.categorie}
                                </span><br>
                                <strong>Commune :</strong> ${pos.commune}<br>
                                <strong>Wilaya :</strong> ${pos.wilaya}<br><br>
                                <div style="display: flex; gap: 10px;">
                                    <button onclick="deletePosition(${pos.id})" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">🗑 Supprimer</button>
                                    <button onclick="editPosition(${pos.id}, '${pos.nom}', '${pos.categorie}', '${pos.commune}', '${pos.wilaya}')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">✏️ Modifier</button>
                                </div>
                            </div>
                        `);
                    marker.nom = pos.nom.toLowerCase();
                    marker.categorie = pos.categorie;
                    marker.commune = pos.commune;
                    marker.wilaya = pos.wilaya;




                    markers.push(marker);
                });
            });
    }

    loadMarkers();

    // Fonction pour créer la légende
    function createLegend() {
        const legendContent = document.getElementById('legendContent');
        legendContent.innerHTML = '';

        Object.keys(categoryIcons).forEach(category => {
            const config = categoryIcons[category];
            const legendItem = document.createElement('div');
            legendItem.style.cssText = `
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                padding: 3px;
                border-radius: 3px;
            `;

            legendItem.innerHTML = `
                <div style="
                    background-color: ${config.color};
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    border: 2px solid white;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    margin-right: 8px;
                    flex-shrink: 0;
                ">
                    ${config.icon}
                </div>
                <span style="font-size: 11px; color: #333;">${category}</span>
            `;

            legendContent.appendChild(legendItem);
        });
    }

    // Fonction pour basculer l'affichage de la légende
    function toggleLegend() {
        const legend = document.getElementById('legend');
        const button = legend.querySelector('button');

        if (legend.style.display === 'none') {
            legend.style.display = 'block';
            button.textContent = 'Masquer';
        } else {
            legend.style.display = 'none';
            button.textContent = 'Afficher';
        }
    }

    // Créer la légende au chargement
    createLegend();

    // Fonction pour charger les frontières des wilayas algériennes
    function loadWilayaBoundaries() {
        console.log('Chargement des frontières des wilayas algériennes...');

        // Essayer plusieurs sources de données fiables
        const sources = [
            'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/algeria_provinces.geojson',
            'https://raw.githubusercontent.com/gregoiredavid/algeria-cities/master/algeria-wilayas.geojson',
            'https://raw.githubusercontent.com/nvkelso/natural-earth-vector/master/geojson/ne_10m_admin_1_states_provinces.geojson'
        ];

        tryLoadFromSources(sources, 0);
    }

    // Fonction pour essayer de charger depuis différentes sources
    function tryLoadFromSources(sources, index) {
        if (index >= sources.length) {
            console.log('Toutes les sources externes ont échoué, utilisation des données de test...');
            loadTestBoundaries();
            return;
        }

        const url = sources[index];
        console.log(`Essai de la source ${index + 1}:`, url);

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`Source ${index + 1} réussie:`, data);

                // Si c'est Natural Earth, filtrer pour l'Algérie
                if (url.includes('natural-earth')) {
                    const algerianFeatures = data.features.filter(feature =>
                        feature.properties.admin === 'Algeria' ||
                        feature.properties.sovereignt === 'Algeria' ||
                        (feature.properties.name_en && feature.properties.name_en.toLowerCase().includes('algeria'))
                    );

                    if (algerianFeatures.length > 0) {
                        wilayaBoundaries = {
                            type: 'FeatureCollection',
                            features: algerianFeatures
                        };
                    } else {
                        throw new Error('Pas de données algériennes trouvées');
                    }
                } else {
                    wilayaBoundaries = data;
                }

                showWilayaBoundaries();
            })
            .catch(error => {
                console.log(`Source ${index + 1} échouée:`, error.message);
                // Essayer la source suivante
                tryLoadFromSources(sources, index + 1);
            });
    }

    // Fonction pour charger les frontières des wilayas algériennes
    function loadAlgerianWilayas() {
        console.log('Chargement des frontières des wilayas algériennes...');

        // URL vers les données GeoJSON des wilayas algériennes
        const wilayasUrl = 'https://raw.githubusercontent.com/gregoiredavid/france-geojson/master/departements.geojson';

        // Pour l'Algérie, utilisons une approche différente avec des données locales
        fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/algeria_provinces.geojson')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Impossible de charger les données externes');
                }
                return response.json();
            })
            .then(data => {
                console.log('Données GeoJSON chargées:', data);
                wilayaBoundaries = data;
                showWilayaBoundaries();
            })
            .catch(error => {
                console.log('Erreur avec les données externes, utilisation des données OpenStreetMap...');
                loadFromOpenStreetMap();
            });
    }

    // Fonction pour charger depuis OpenStreetMap Nominatim
    function loadFromOpenStreetMap() {
        console.log('Chargement des frontières via OpenStreetMap...');

        // Charger les frontières des wilayas depuis notre base de données
        fetch('get_wilayas.php')
            .then(response => response.json())
            .then(wilayas => {
                console.log('Wilayas trouvées:', wilayas);
                loadWilayaBoundariesFromOSM(wilayas);
            })
            .catch(error => {
                console.error('Erreur:', error);
                loadTestBoundaries();
            });
    }

    // Fonction pour charger les frontières via l'API Nominatim d'OpenStreetMap
    function loadWilayaBoundariesFromOSM(wilayas) {
        const features = [];
        let loadedCount = 0;

        wilayas.forEach((wilaya, index) => {
            // Construire l'URL pour l'API Nominatim
            const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=geojson&polygon_geojson=1&addressdetails=1&limit=1&q=${encodeURIComponent(wilaya.nom + ', Algeria')}`;

            console.log(`Chargement de ${wilaya.nom}...`);

            fetch(nominatimUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.features && data.features.length > 0) {
                        const feature = data.features[0];
                        feature.properties.id = wilaya.id;
                        feature.properties.nom = wilaya.nom;
                        feature.properties.code = String(wilaya.id).padStart(2, '0');
                        features.push(feature);
                        console.log(`${wilaya.nom} chargée avec succès`);
                    } else {
                        console.log(`Pas de données trouvées pour ${wilaya.nom}`);
                    }

                    loadedCount++;
                    if (loadedCount === wilayas.length) {
                        // Toutes les wilayas ont été traitées
                        wilayaBoundaries = {
                            type: 'FeatureCollection',
                            features: features
                        };
                        console.log(`${features.length} frontières chargées`);
                        showWilayaBoundaries();
                    }
                })
                .catch(error => {
                    console.error(`Erreur pour ${wilaya.nom}:`, error);
                    loadedCount++;
                    if (loadedCount === wilayas.length) {
                        if (features.length > 0) {
                            wilayaBoundaries = {
                                type: 'FeatureCollection',
                                features: features
                            };
                            showWilayaBoundaries();
                        } else {
                            loadTestBoundaries();
                        }
                    }
                });

            // Délai entre les requêtes pour respecter les limites de l'API
            setTimeout(() => {}, index * 1000);
        });
    }

    // Fonction de test avec des données simples
    function loadTestBoundaries() {
        console.log('Chargement des frontières de test...');

        wilayaBoundaries = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "id": 1,
                        "nom": "Béchar",
                        "code": "01"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [-2.5, 31.0], [-1.5, 31.0], [-1.0, 31.5], [-0.5, 32.0],
                            [-0.5, 32.5], [-1.0, 33.0], [-2.0, 33.0], [-3.0, 32.5],
                            [-3.5, 32.0], [-3.0, 31.5], [-2.5, 31.0]
                        ]]
                    }
                },
                {
                    "type": "Feature",
                    "properties": {
                        "id": 2,
                        "nom": "Adrar",
                        "code": "02"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [-1.0, 27.0], [0.5, 27.0], [1.0, 27.5], [1.5, 28.0],
                            [1.5, 28.5], [1.0, 29.0], [0.0, 29.0], [-1.0, 28.5],
                            [-1.5, 28.0], [-1.0, 27.5], [-1.0, 27.0]
                        ]]
                    }
                }
            ]
        };

        showWilayaBoundaries();
    }

    // Fonction pour charger simplement les contours de l'Algérie
    function loadCountryBoundary() {
        console.log('Chargement des contours de l\'Algérie...');

        // Utiliser l'API REST Countries qui fournit des données géographiques
        fetch('https://restcountries.com/v3.1/name/algeria?fields=name,cca2,latlng,area')
            .then(response => response.json())
            .then(data => {
                console.log('Données pays:', data);
                // Charger les contours depuis World Bank ou Natural Earth
                loadAlgeriaFromWorldBank();
            })
            .catch(error => {
                console.log('Erreur REST Countries, essai avec les données World Bank...');
                loadAlgeriaFromWorldBank();
            });
    }

    // Charger les contours de l'Algérie depuis World Bank
    function loadAlgeriaFromWorldBank() {
        // URL vers les données géographiques de la Banque Mondiale
        const worldBankUrl = 'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson';

        fetch(worldBankUrl)
            .then(response => response.json())
            .then(data => {
                console.log('Données World Bank reçues');

                // Filtrer pour l'Algérie
                const algeria = data.features.filter(feature =>
                    feature.properties.NAME === 'Algeria' ||
                    feature.properties.name === 'Algeria' ||
                    feature.properties.NAME_EN === 'Algeria' ||
                    (feature.properties.ISO_A3 && feature.properties.ISO_A3 === 'DZA')
                );

                if (algeria.length > 0) {
                    console.log('Algérie trouvée:', algeria[0]);
                    wilayaBoundaries = {
                        type: 'FeatureCollection',
                        features: algeria
                    };
                    showWilayaBoundaries();
                } else {
                    console.log('Algérie non trouvée, utilisation des données simplifiées...');
                    loadSimplifiedAlgeria();
                }
            })
            .catch(error => {
                console.error('Erreur World Bank:', error);
                loadSimplifiedAlgeria();
            });
    }

    // Fonction avec des contours simplifiés de l'Algérie
    function loadSimplifiedAlgeria() {
        console.log('Chargement des contours simplifiés de l\'Algérie...');

        wilayaBoundaries = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "properties": {
                        "name": "Algérie",
                        "nom": "Algérie",
                        "id": 1,
                        "code": "DZ"
                    },
                    "geometry": {
                        "type": "Polygon",
                        "coordinates": [[
                            [-8.684, 27.395], [11.986, 27.395], [11.986, 37.093],
                            [-8.684, 37.093], [-8.684, 27.395]
                        ]]
                    }
                }
            ]
        };

        showWilayaBoundaries();
    }

    // Fonction pour afficher les frontières des wilayas
    function showWilayaBoundaries() {
        console.log('Affichage des frontières...', wilayaBoundaries);

        if (!wilayaBoundaries || !wilayaBoundaries.features) {
            console.error('Pas de données de frontières disponibles');
            return;
        }

        wilayaBoundaries.features.forEach((feature, index) => {
            console.log(`Traitement de la wilaya ${index + 1}:`, feature.properties.nom);

            try {
                const wilayaLayer = L.geoJSON(feature, {
                    style: {
                        color: '#2E8B57',
                        weight: 3,
                        opacity: 0.8,
                        fillColor: getWilayaColor(feature.properties.nom),
                        fillOpacity: 0.2,
                        dashArray: '5, 5'
                    },
                    onEachFeature: function(feature, layer) {
                        // Popup simple
                        layer.bindPopup(`
                            <div style="text-align: center; min-width: 150px;">
                                <h4 style="margin: 5px 0; color: #2E8B57;">🏛️ ${feature.properties.nom}</h4>
                                <p style="margin: 5px 0;"><strong>Code:</strong> ${feature.properties.code}</p>
                            </div>
                        `);

                        // Tooltip au survol
                        layer.bindTooltip(`Wilaya de ${feature.properties.nom}`, {
                            permanent: false,
                            direction: 'center'
                        });

                        // Effet de survol
                        layer.on('mouseover', function(e) {
                            layer.setStyle({
                                weight: 4,
                                fillOpacity: 0.4,
                                color: '#FF6347'
                            });
                        });

                        layer.on('mouseout', function(e) {
                            layer.setStyle({
                                weight: 3,
                                fillOpacity: 0.2,
                                color: '#2E8B57'
                            });
                        });
                    }
                }).addTo(map);

                boundaryLayers.push(wilayaLayer);
                console.log(`Wilaya ${feature.properties.nom} ajoutée à la carte`);

            } catch (error) {
                console.error(`Erreur lors de l'ajout de la wilaya ${feature.properties.nom}:`, error);
            }
        });

        console.log(`${boundaryLayers.length} frontières ajoutées à la carte`);
    }

    // Fonction pour obtenir une couleur unique pour chaque wilaya
    function getWilayaColor(wilayaName) {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];
        const hash = wilayaName.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0);
        return colors[Math.abs(hash) % colors.length];
    }

    // Fonction pour filtrer par wilaya depuis la popup
    function filterByWilaya(wilayaName) {
        const wilayaSelect = document.getElementById('wilayaFilter');
        for (let i = 0; i < wilayaSelect.options.length; i++) {
            if (wilayaSelect.options[i].text === wilayaName) {
                wilayaSelect.selectedIndex = i;
                break;
            }
        }
        filterMarkers();
        map.closePopup();
    }

    // Variable pour suivre l'état des frontières
    let boundariesVisible = true;

    // Fonction pour masquer/afficher les frontières
    function toggleWilayaBoundaries() {
        const button = document.querySelector('button[onclick="toggleWilayaBoundaries()"]');

        if (boundariesVisible) {
            // Masquer les frontières
            boundaryLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            button.innerHTML = '🗾 Afficher Frontières';
            button.style.background = '#6c757d';
            boundariesVisible = false;
        } else {
            // Afficher les frontières
            boundaryLayers.forEach(layer => {
                map.addLayer(layer);
            });
            button.innerHTML = '🗾 Masquer Frontières';
            button.style.background = '#17a2b8';
            boundariesVisible = true;
        }
    }

    // Charger les frontières au démarrage
    setTimeout(() => {
        console.log('Démarrage du chargement des frontières...');
        loadWilayaBoundaries();
    }, 1000);

    // Ajout de nouvelle position
    map.on('click', function(e) {
        currentLatLng = e.latlng;
        document.getElementById('lat').value = e.latlng.lat.toFixed(6);
        document.getElementById('lng').value = e.latlng.lng.toFixed(6);
        document.getElementById('formContainer').style.display = 'block';

    });

    document.getElementById('positionForm').addEventListener('submit', function(e) {
        e.preventDefault();
        let nom = document.getElementById('nom').value;
        let categorie = document.getElementById('categorie').value;

        if (editingId !== null) {
            // 🔄 Mise à jour
            const latInput = document.getElementById('lat').value;
            const lngInput = document.getElementById('lng').value;
            const communeSelect = document.getElementById('commune');
            const commune = communeSelect.value;

            // Conversion des coordonnées en nombres
            const lat = parseFloat(latInput);
            const lng = parseFloat(lngInput);

            if (isNaN(lat) || isNaN(lng)) {
                alert("Veuillez vérifier que les coordonnées sont valides.");
                return;
            }

            if (!commune) {
                alert("Veuillez sélectionner une commune.");
                return;
            }

            // Afficher les données qui seront envoyées pour le débogage
            console.log('Données à mettre à jour:', {
                id: editingId,
                lat,
                lng,
                nom,
                categorie,
                commune
            });

            fetch('update_position.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `id=${editingId}&lat=${lat}&lng=${lng}&nom=${encodeURIComponent(nom)}&categorie=${encodeURIComponent(categorie)}&commune=${commune}`
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.error || `Erreur ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        cancelForm();
                        loadMarkers();
                    } else {
                        alert(data.error || "Erreur lors de la mise à jour de la position.");
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert("Une erreur est survenue lors de la mise à jour: " + error.message);
                });
        } else {
            // ➕ Nouvelle position
            let latInput = document.getElementById('lat').value;
            let lngInput = document.getElementById('lng').value;

            let lat = latInput !== "" ? parseFloat(latInput) : currentLatLng?.lat;
            let lng = lngInput !== "" ? parseFloat(lngInput) : currentLatLng?.lng;

            if (isNaN(lat) || isNaN(lng)) {
                alert("Veuillez saisir une latitude/longitude valides ou cliquer sur la carte.");
                return;
            }


            fetch('save_position.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `lat=${lat}&lng=${lng}&nom=${encodeURIComponent(nom)}&categorie=${encodeURIComponent(categorie)}&commune=${encodeURIComponent(document.getElementById('commune').value)}`
            }).then(() => {
                cancelForm();
                loadMarkers();
            });

        }
    });



    // Charger les wilayas au démarrage
    function loadWilayas() {
        fetch('get_wilayas.php')
            .then(res => res.json())
            .then(data => {
                const wilayaSelect = document.getElementById('wilaya');
                data.forEach(w => {
                    const option = document.createElement('option');
                    option.value = w.id;
                    option.textContent = w.nom;
                    wilayaSelect.appendChild(option);
                });
                loadCommunes(); // charger les communes pour la 1ère wilaya
            });
    }

    //Charger les communes d’une wilaya
    function loadCommunes() {
        const wilayaId = document.getElementById('wilaya').value;
        fetch('get_communes.php?wilaya=' + wilayaId)
            .then(res => res.json())
            .then(data => {
                const communeSelect = document.getElementById('commune');
                communeSelect.innerHTML = '';
                data.forEach(c => {
                    const option = document.createElement('option');
                    option.value = c.id;
                    option.textContent = c.nom;
                    communeSelect.appendChild(option);
                });
            });
    }

    window.onload = loadWilayas;

    function savePosition() {
        const nom = document.getElementById('nom').value;
        const categorie = document.getElementById('categorie').value;
        const latInput = document.getElementById('lat').value;
        const lngInput = document.getElementById('lng').value;

        // Conversion des coordonnées en nombres
        const lat = parseFloat(latInput);
        const lng = parseFloat(lngInput);

        const communeSelect = document.getElementById('commune');
        if (!communeSelect) {
            console.log("❌ <select id='commune'> introuvable !");
            return;
        }

        // Vérification que la commune est sélectionnée
        if (!communeSelect.value) {
            alert("Veuillez sélectionner une commune.");
            return;
        }

        const commune = communeSelect.value;
        const communeNom = communeSelect.options[communeSelect.selectedIndex]?.text || "";

        //console.log('Commune sélectionnée (ID) :', commune);
        //console.log('Commune sélectionnée (Nom) :', communeNom);

        if (isNaN(lat) || isNaN(lng)) {
            alert("Veuillez vérifier que les coordonnées sont valides.");
            return;
        }

        // Afficher les données qui seront envoyées pour le débogage
        console.log('Données à envoyer:', {
            lat,
            lng,
            nom,
            categorie,
            commune
        });

        fetch('save_position.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `lat=${lat}&lng=${lng}&nom=${encodeURIComponent(nom)}&categorie=${encodeURIComponent(categorie)}&commune=${commune}`
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(data.error || `Erreur ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    cancelForm();
                    loadMarkers();
                } else {
                    alert(data.error || "Erreur lors de l'enregistrement de la position.");
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert("Une erreur est survenue lors de l'enregistrement: " + error.message);
            });
    }





    function loadWilayasFilter() {
        fetch('get_wilayas.php')
            .then(res => res.json())
            .then(data => {
                const select = document.getElementById('wilayaFilter');
                data.forEach(w => {
                    const option = document.createElement('option');
                    option.value = w.id;
                    option.textContent = w.nom;
                    select.appendChild(option);
                });
            });
    }

    // Fonction pour charger toutes les communes dans le filtre
    function loadAllCommunesFilter() {
        fetch('get_all_communes.php')
            .then(res => res.json())
            .then(data => {
                const communeSelect = document.getElementById('communeFilter');
                // Garder l'option "-- Toutes --"
                communeSelect.innerHTML = '<option value="">-- Toutes --</option>';
                data.forEach(c => {
                    const option = document.createElement('option');
                    option.value = c.nom;
                    option.textContent = `${c.nom} (${c.wilaya})`;
                    communeSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement de toutes les communes:', error);
            });
    }
    window.onload = () => {
        loadWilayas();
        loadWilayasFilter(); // <== charge le filtre aussi
        loadAllCommunesFilter(); // <== charge toutes les communes dans le filtre
    };





    //-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- //

    function cancelForm() {
        document.getElementById('formContainer').style.display = 'none';
        document.getElementById('positionForm').reset();
    }

    // Suppression de position
    function deletePosition(id) {
        if (!confirm("Supprimer cette position ?")) return;

        fetch('delete_position.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (markerMap[id]) {
                        map.removeLayer(markerMap[id]);
                        delete markerMap[id];
                        map.closePopup();
                    }
                    loadMarkers();
                } else {
                    throw new Error(data.error || 'Erreur lors de la suppression');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors de la suppression: ' + error.message);
            });
    }


    // Recherche de position
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        markers.forEach(m => {
            const match = m.nom.includes(searchTerm);
            if (match) {
                m.addTo(map);
            } else {
                map.removeLayer(m);
            }
        });
    });
    let editingId = null;

    function editPosition(id, nom, categorie, commune, wilaya) {
        editingId = id;
        document.getElementById('nom').value = nom;
        document.getElementById('categorie').value = categorie;

        // Récupérer les coordonnées de la position à modifier
        fetch(`get_position.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remplir les champs de latitude et longitude
                    document.getElementById('lat').value = data.position.latitude;
                    document.getElementById('lng').value = data.position.longitude;

                    // Sélectionner la wilaya et charger les communes correspondantes
                    const wilayaSelect = document.getElementById('wilaya');
                    for (let i = 0; i < wilayaSelect.options.length; i++) {
                        if (wilayaSelect.options[i].text === wilaya) {
                            wilayaSelect.selectedIndex = i;
                            break;
                        }
                    }

                    // Charger les communes de la wilaya sélectionnée
                    loadCommunes().then(() => {
                        // Sélectionner la commune
                        const communeSelect = document.getElementById('commune');
                        for (let i = 0; i < communeSelect.options.length; i++) {
                            if (communeSelect.options[i].text === commune) {
                                communeSelect.selectedIndex = i;
                                break;
                            }
                        }
                    });
                } else {
                    console.error('Erreur lors de la récupération des coordonnées:', data.error);
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
            });

        document.getElementById('formContainer').style.display = 'block';
    }

    function filterMarkers() {
        const nameSearch = document.getElementById('searchInput').value.toLowerCase();
        const selectedCategory = document.getElementById('categoryFilter').value;
        const selectedCommune = document.getElementById('communeFilter').value;
        const wilayaSelected = document.getElementById('wilayaFilter');
        const selectedWilayaId = wilayaSelected.value; // ex: "8"
        const selectedWilayaName = wilayaSelected.options[wilayaSelected.selectedIndex].text; // ex: "Béchar"

        // Correction : considérer l'option "Toutes" comme aucune sélection
        const isAllWilayas = selectedWilayaId === "" || selectedWilayaName.toLowerCase().includes("toute");

        // Debug information
        console.log('Filtrage - Commune sélectionnée:', selectedCommune);
        console.log('Filtrage - Wilaya sélectionnée:', selectedWilayaName);

        // Désactiver le zoom si toutes les wilayas sont sélectionnées
        if (isAllWilayas) {
            map.scrollWheelZoom.disable();
        } else {
            map.scrollWheelZoom.enable();
        }

        //console.log('Catégorie sélectionnée :', selectedCategory);
        //console.log('Commune sélectionnée :', selectedCommune);
        //console.log('Wilaya sélectionnée :', selectedWilayaName);




        let foundMarkers = [];


        markers.forEach(marker => {
            const matchName = marker.nom.includes(nameSearch);
            const matchCategory = selectedCategory === "" || marker.categorie === selectedCategory;
            const matchCommune = selectedCommune === "" || (marker.commune && marker.commune.toString()
                .toLowerCase().trim() === selectedCommune.toString().toLowerCase().trim());
            const matchWilaya = isAllWilayas || (marker.wilaya && marker.wilaya.toString().toLowerCase()
                .trim() ===
                selectedWilayaName.toString().toLowerCase().trim());

            // Debug pour le premier marqueur seulement
            if (marker.options && marker.options.id && selectedCommune !== "") {
                console.log(
                    `Marker ${marker.options.id}: commune="${marker.commune}", selectedCommune="${selectedCommune}", match=${matchCommune}`
                );
            }

            if (matchName && matchCategory && matchCommune && matchWilaya) {
                marker.addTo(map);
                foundMarkers.push(marker); // ✅ ajouter le marqueur trouvé
            } else {
                map.removeLayer(marker);
            }
        });

        // ✅ Si un seul résultat, zoomer et ouvrir le popup
        if (foundMarkers.length === 1) {
            const latlng = foundMarkers[0].getLatLng();
            map.setView(latlng, 16); // zoom niveau 16
            foundMarkers[0].openPopup();
        }

        // ✅ Si plusieurs résultats (optionnel) — zoom automatique sur tous
        else if (foundMarkers.length > 1) {
            const group = L.featureGroup(foundMarkers);
            map.fitBounds(group.getBounds().pad(0.2));
        }
    }

    function onWilayaChange() {
        const selectedWilaya = document.getElementById('wilayaFilter').value;
        // console.log('Wilaya sélectionnée (ID) :', selectedWilaya);

        const communeSelect = document.getElementById('communeFilter');
        communeSelect.innerHTML = '<option value="">-- Toutes --</option>';

        // Correction : recharger les marqueurs à chaque changement de wilaya
        loadMarkers(selectedWilaya);

        if (selectedWilaya === "") {
            filterMarkers();
            return;
        }

        fetch('get_communes.php?wilaya=' + encodeURIComponent(selectedWilaya))
            .then(res => res.json())
            .then(data => {
                // console.log('Communes reçues :', data);
                data.forEach(c => {
                    const option = document.createElement('option');
                    option.value = c.nom; // <= ici était l’erreur
                    option.textContent = c.nom;
                    communeSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Erreur lors du chargement des communes:', error);
            });

        filterMarkers();
    }
    const protectCopyright = () => {
        const el = document.getElementById('copyright');
        if (!el) {
            const newEl = document.createElement('div');
            newEl.id = 'copyright';
            newEl.textContent = '© 2025 Boumares Abdelhafid. Tous droits réservés.';
            Object.assign(newEl.style, {
                position: 'fixed',
                bottom: '10px',
                right: '10px',
                fontSize: '13px',
                background: 'rgba(255,255,255,0.8)',
                padding: '6px 10px',
                borderRadius: '5px',
                color: '#333',
                fontWeight: 'bold',
                zIndex: 99999,
                pointerEvents: 'none',
                userSelect: 'none'
            });
            document.body.appendChild(newEl);
        }
    };


    setInterval(protectCopyright, 2000);

    document.getElementById('searchInput').addEventListener('input', filterMarkers);
    document
        .getElementById('categoryFilter').addEventListener('change', filterMarkers);
    document.getElementById(
        'communeFilter').addEventListener('change', filterMarkers);

    // Fonctions d'exportation
    function exportCSV() {
        // Récupérer les filtres actuels
        const searchTerm = document.getElementById('searchInput').value;
        const selectedCategory = document.getElementById('categoryFilter').value;
        const selectedCommune = document.getElementById('communeFilter').value;
        const wilayaSelect = document.getElementById('wilayaFilter');
        const selectedWilaya = wilayaSelect.options[wilayaSelect.selectedIndex].text;

        // Construire l'URL avec les paramètres de filtre
        let url = 'export_csv.php?';
        if (searchTerm) url += `search=${encodeURIComponent(searchTerm)}&`;
        if (selectedCategory) url += `categorie=${encodeURIComponent(selectedCategory)}&`;
        if (selectedWilaya && selectedWilaya !== '-- Toutes --') url += `wilaya=${encodeURIComponent(selectedWilaya)}&`;
        if (selectedCommune) url += `commune=${encodeURIComponent(selectedCommune)}&`;

        // Rediriger vers l'URL d'exportation
        window.location.href = url;
    }

    function exportPDF() {
        // Vérifier si TCPDF est installé
        fetch('tcpdf/tcpdf.php')
            .then(response => {
                if (!response.ok) {
                    if (confirm(
                            'La bibliothèque TCPDF n\'est pas installée. Voulez-vous l\'installer maintenant?')) {
                        window.location.href = 'install_tcpdf.php';
                    }
                    return;
                }

                // Récupérer les filtres actuels
                const searchTerm = document.getElementById('searchInput').value;
                const selectedCategory = document.getElementById('categoryFilter').value;
                const selectedCommune = document.getElementById('communeFilter').value;
                const wilayaSelect = document.getElementById('wilayaFilter');
                const selectedWilaya = wilayaSelect.options[wilayaSelect.selectedIndex].text;

                // Construire l'URL avec les paramètres de filtre
                let url = 'export_pdf.php?';
                if (searchTerm) url += `search=${encodeURIComponent(searchTerm)}&`;
                if (selectedCategory) url += `categorie=${encodeURIComponent(selectedCategory)}&`;
                if (selectedWilaya && selectedWilaya !== '-- Toutes --') url +=
                    `wilaya=${encodeURIComponent(selectedWilaya)}&`;
                if (selectedCommune) url += `commune=${encodeURIComponent(selectedCommune)}&`;

                // Rediriger vers l'URL d'exportation
                window.location.href = url;
            })
            .catch(error => {
                console.error('Erreur lors de la vérification de TCPDF:', error);
                if (confirm('La bibliothèque TCPDF n\'est pas installée. Voulez-vous l\'installer maintenant?')) {
                    window.location.href = 'install_tcpdf.php';
                }
            });
    }
    </script>

</body>

</html>