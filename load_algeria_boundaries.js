// Fonction pour charger les frontières des wilayas algériennes
function loadAlgeriaBoundaries() {
    console.log('Chargement des frontières algériennes...');
    
    // Utiliser l'API REST Countries pour obtenir les frontières de l'Algérie
    const algeriaUrl = 'https://restcountries.com/v3.1/name/algeria';
    
    fetch(algeriaUrl)
        .then(response => response.json())
        .then(data => {
            console.log('Données pays reçues:', data);
            // Maintenant charger les subdivisions administratives
            loadWilayasFromNaturalEarth();
        })
        .catch(error => {
            console.log('Erreur avec REST Countries, essai avec Natural Earth...');
            loadWilayasFromNaturalEarth();
        });
}

// Charger depuis Natural Earth Data (source fiable pour les données géographiques)
function loadWilayasFromNaturalEarth() {
    // URL vers les données Natural Earth pour les subdivisions administratives de l'Algérie
    const naturalEarthUrl = 'https://raw.githubusercontent.com/nvkelso/natural-earth-vector/master/geojson/ne_10m_admin_1_states_provinces.geojson';
    
    fetch(naturalEarthUrl)
        .then(response => response.json())
        .then(data => {
            console.log('Données Natural Earth reçues');
            
            // Filtrer pour ne garder que l'Algérie
            const algerianProvinces = data.features.filter(feature => 
                feature.properties.admin === 'Algeria' || 
                feature.properties.sovereignt === 'Algeria' ||
                feature.properties.name_en && feature.properties.name_en.includes('Algeria')
            );
            
            console.log('Provinces algériennes trouvées:', algerianProvinces.length);
            
            if (algerianProvinces.length > 0) {
                wilayaBoundaries = {
                    type: 'FeatureCollection',
                    features: algerianProvinces
                };
                showWilayaBoundaries();
            } else {
                console.log('Aucune province algérienne trouvée, utilisation des données de test...');
                loadTestBoundaries();
            }
        })
        .catch(error => {
            console.error('Erreur Natural Earth:', error);
            loadTestBoundaries();
        });
}

// Fonction de fallback avec des données simplifiées
function loadSimplifiedBoundaries() {
    console.log('Chargement des frontières simplifiées...');
    
    // Données simplifiées pour l'Algérie (contour général du pays)
    const algeriaOutline = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "name": "Algérie",
                    "admin": "Algeria"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[
                        [-8.684, 27.395],
                        [11.986, 27.395],
                        [11.986, 37.093],
                        [-8.684, 37.093],
                        [-8.684, 27.395]
                    ]]
                }
            }
        ]
    };
    
    wilayaBoundaries = algeriaOutline;
    showWilayaBoundaries();
}
